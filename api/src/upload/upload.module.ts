import { Modu<PERSON> } from '@nestjs/common';
import { UploadService } from './upload.service';
import { UploadController } from './upload.controller';
import { MulterModule } from '@nestjs/platform-express';
import { memoryStorage } from 'multer';
import { AwsConfigService } from './aws-config.service';
import { UPLOAD_SECURITY_CONFIG } from './upload.config';
import { UploadRateLimitGuard } from './guards/upload-rate-limit.guard';

@Module({
  imports: [
    MulterModule.registerAsync({
      useFactory: () => ({
        storage: memoryStorage(),
        limits: {
          fileSize: UPLOAD_SECURITY_CONFIG.MAX_FILE_SIZE,
          files: UPLOAD_SECURITY_CONFIG.MAX_FILES,
          fields: UPLOAD_SECURITY_CONFIG.MAX_FIELDS,
          fieldNameSize: UPLOAD_SECURITY_CONFIG.MAX_FIELD_NAME_SIZE,
          fieldSize: UPLOAD_SECURITY_CONFIG.MAX_FIELD_SIZE,
          headerPairs: UPLOAD_SECURITY_CONFIG.MAX_HEADER_PAIRS,
        },
        fileFilter: (_req, file, callback) => {
          if (
            UPLOAD_SECURITY_CONFIG.ALLOWED_MIME_TYPES.includes(
              file.mimetype as any,
            )
          ) {
            callback(null, true);
          } else {
            callback(
              new Error(UPLOAD_SECURITY_CONFIG.ERRORS.INVALID_FILE_TYPE),
              false,
            );
          }
        },
      }),
    }),
  ],
  exports: [AwsConfigService, UploadService],
  controllers: [UploadController],
  providers: [UploadService, AwsConfigService, UploadRateLimitGuard],
})
export class UploadModule {}
