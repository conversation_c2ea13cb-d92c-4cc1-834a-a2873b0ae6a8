import {
  Controller,
  FileTypeValidator,
  Logger,
  ParseFilePipe,
  Post,
  UploadedFile,
  UploadedFiles,
  UseInterceptors,
  UseGuards,
} from '@nestjs/common';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import { UploadService } from './upload.service';
import { UseRoles } from 'nest-access-control';
import { AppClients } from '@app/shared/constants/auth.constants';
import { CLIENT_TYPE } from '@/guards/request-validation.decorator';
import { CustomMaxFileSizeValidator } from '@/validators/custom-max-file-size.validator';
import { UPLOAD_SECURITY_CONFIG } from './upload.config';
import { UploadRateLimitGuard } from './guards/upload-rate-limit.guard';
import { ImageUploadRateLimitGuard } from './guards/image-upload-rate-limit.guard';
import {
  ApiConsumes,
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiOkResponse,
  ApiInternalServerErrorResponse,
  ApiBadRequestResponse,
  ApiBody,
} from '@nestjs/swagger';
import { FileUploadRoutes } from '@app/shared/constants/file-upload.constants';

@Controller({ version: '1', path: 'upload' })
@ApiTags('Upload')
export class UploadController {
  private readonly logger = new Logger(UploadController.name);
  constructor(private readonly uploadService: UploadService) {}

  @Post(FileUploadRoutes.UPLOAD_FILE)
  @UseGuards(UploadRateLimitGuard)
  @CLIENT_TYPE(AppClients.WEB, AppClients.MOBILE)
  @ApiOperation({
    summary: 'Upload a file',
    description: 'Upload a file (PNG, JPEG, JPG,) with a maximum size of 2MB',
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'File to upload (PNG, JPEG, JPG)',
        },
      },
      required: ['file'],
    },
  })
  @ApiOkResponse({
    description: 'File uploaded successfully',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'File uploaded successfully',
        },
        fileUrl: {
          type: 'string',
          example: 'https://cdn.example.com/bucket-name/123456789-image.jpg',
        },
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'Invalid file format, file too large, or empty file',
  })
  @ApiInternalServerErrorResponse({
    description: 'S3 upload error or server configuration issue',
  })
  @ApiBearerAuth()
  @UseInterceptors(FileInterceptor('file'))
  @UseRoles({ resource: 'upload', action: 'create', possession: 'any' })
  async uploadFile(
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new FileTypeValidator({
            fileType: UPLOAD_SECURITY_CONFIG.ALL_EXTENSIONS,
          }),
          new CustomMaxFileSizeValidator({
            maxSize: UPLOAD_SECURITY_CONFIG.MAX_FILE_SIZE,
          }),
        ],
        fileIsRequired: true,
      }),
    )
    file: Express.Multer.File,
  ) {
    try {
      const fileUrl = await this.uploadService.uploadFileToS3(file);
      return {
        message: 'File uploaded successfully',
        fileUrl,
      };
    } catch (error) {
      this.logger.error('Upload error:', error);
      throw error;
    }
  }

  @Post(FileUploadRoutes.UPLOAD_IMAGES)
  @UseGuards(ImageUploadRateLimitGuard)
  @CLIENT_TYPE(AppClients.WEB, AppClients.MOBILE)
  @ApiOperation({
    summary: 'Upload multiple images',
    description:
      'Upload multiple images (PNG, JPEG, JPG) with a maximum of 5 files, 2MB each',
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        files: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary',
          },
          description: 'Image files to upload (PNG, JPEG, JPG)',
        },
      },
      required: ['files'],
    },
  })
  @ApiOkResponse({
    description: 'Images uploaded successfully',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Images uploaded successfully',
        },
        fileUrls: {
          type: 'array',
          items: {
            type: 'string',
            example: 'https://cdn.example.com/bucket-name/123456789-image.jpg',
          },
        },
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'Invalid file format, file too large, or too many files',
  })
  @ApiInternalServerErrorResponse({
    description: 'S3 upload error or server configuration issue',
  })
  @ApiBearerAuth()
  @UseInterceptors(
    FilesInterceptor('files', UPLOAD_SECURITY_CONFIG.MAX_FILES_MULTIPLE),
  )
  @UseRoles({ resource: 'upload', action: 'create', possession: 'any' })
  async uploadImages(
    @UploadedFiles(
      new ParseFilePipe({
        validators: [
          new FileTypeValidator({
            fileType: UPLOAD_SECURITY_CONFIG.IMAGE_EXTENSIONS,
          }),
          new CustomMaxFileSizeValidator({
            maxSize: UPLOAD_SECURITY_CONFIG.MAX_IMAGE_SIZE,
          }),
        ],
        fileIsRequired: true,
      }),
    )
    files: Express.Multer.File[],
  ) {
    try {
      const uploadPromises = files.map((file) =>
        this.uploadService.uploadFileToS3(file),
      );
      const fileUrls = await Promise.all(uploadPromises);

      return {
        message: 'Images uploaded successfully',
        fileUrls,
      };
    } catch (error) {
      this.logger.error('Upload error:', error);
      throw error;
    }
  }
}
