# Upload API Endpoints

## Overview
The upload module provides two distinct endpoints for different use cases:

## 1. File Upload Endpoint
**Endpoint:** `POST /v1/upload/file`

### Purpose
- General document uploads
- Business documents and data files
- Single file uploads only

### Specifications
- **File Types:** CSV, PDF, DOCX, PPTX
- **Max File Size:** 10MB
- **Max Files:** 1 file per request
- **Rate Limit:** 10 uploads per minute per IP

### Request Format
```typescript
Content-Type: multipart/form-data

{
  file: File // Single document file
}
```

### Response Format
```typescript
{
  message: "File uploaded successfully",
  fileUrl: "https://cdn.example.com/bucket-name/document.pdf"
}
```

### Use Cases
- Document submissions
- CSV data imports
- Report uploads
- Administrative documents

---

## 2. Image Upload Endpoint
**Endpoint:** `POST /v1/upload/images`

### Purpose
- Multiple image uploads
- Quiz response images
- Review images
- Gallery uploads

### Specifications
- **File Types:** PNG, JPEG, JPG only
- **Max File Size:** 2MB per image
- **Max Files:** 10 images per request
- **Rate Limit:** 20 uploads per minute per IP

### Request Format
```typescript
Content-Type: multipart/form-data

{
  files: File[] // Array of image files (max 10)
}
```

### Response Format
```typescript
{
  message: "Images uploaded successfully",
  fileUrls: [
    "https://cdn.example.com/bucket-name/image1.jpg",
    "https://cdn.example.com/bucket-name/image2.png",
    "https://cdn.example.com/bucket-name/image3.jpeg"
  ]
}
```

### Use Cases
- Quiz response submissions
- Product review images
- Gallery uploads
- Multiple photo submissions

---

## Security Features

### Common Security
- JWT authentication required
- Role-based access control
- Content-type validation
- Security headers (X-Content-Type-Options, X-Frame-Options)
- Memory storage with limits
- Request size validation

### File-Specific Security
- MIME type validation at multer level
- File extension validation
- Separate rate limiting per endpoint
- Context-appropriate file size limits

### Error Handling
- Detailed validation error messages
- Proper HTTP status codes
- Comprehensive logging
- Graceful failure handling

---

## Rate Limiting Details

### File Upload Rate Limiting
- **Limit:** 10 uploads per minute
- **Scope:** Per IP address
- **Window:** 60 seconds rolling window
- **Error:** 429 Too Many Requests

### Image Upload Rate Limiting
- **Limit:** 20 uploads per minute
- **Scope:** Per IP address  
- **Window:** 60 seconds rolling window
- **Error:** 429 Too Many Requests

### Rate Limit Headers
```
X-RateLimit-Limit: 10
X-RateLimit-Remaining: 7
X-RateLimit-Reset: 1640995200
```

---

## Integration Examples

### Frontend JavaScript
```javascript
// Single file upload
const formData = new FormData();
formData.append('file', documentFile);

const response = await fetch('/v1/upload/file', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`
  },
  body: formData
});

// Multiple image upload
const imageFormData = new FormData();
imageFiles.forEach(file => {
  imageFormData.append('files', file);
});

const imageResponse = await fetch('/v1/upload/images', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`
  },
  body: imageFormData
});
```

### cURL Examples
```bash
# File upload
curl -X POST \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@document.pdf" \
  http://localhost:3000/v1/upload/file

# Image upload
curl -X POST \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "files=@image1.jpg" \
  -F "files=@image2.png" \
  http://localhost:3000/v1/upload/images
```
