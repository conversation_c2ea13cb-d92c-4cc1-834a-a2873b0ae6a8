import {
  Injectable,
  NestMiddleware,
  BadRequestException,
} from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { UPLOAD_SECURITY_CONFIG } from '../upload.config';

/**
 * Upload Security Middleware
 * Provides additional security checks for file upload requests
 */
@Injectable()
export class UploadSecurityMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    // Check content-length header to prevent large requests
    const contentLength = req.headers['content-length'];
    if (contentLength) {
      const size = parseInt(contentLength, 10);
      if (size > UPLOAD_SECURITY_CONFIG.MAX_FILE_SIZE * 2) {
        // Allow some overhead for multipart
        throw new BadRequestException('Request too large');
      }
    }

    // Validate content-type for multipart uploads
    const contentType = req.headers['content-type'];
    if (contentType && !contentType.startsWith('multipart/form-data')) {
      throw new BadRequestException('Invalid content type for file upload');
    }

    // Add security headers
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'DENY');

    next();
  }
}
