/**
 * Upload security configuration constants
 * Centralized configuration for file upload limits and security settings
 */

export const UPLOAD_SECURITY_CONFIG = {
  MAX_FILE_SIZE: 10 * 1024 * 1024,
  MAX_IMAGE_SIZE: 2 * 1024 * 1024,
  MAX_FILES_SINGLE: 1,
  MAX_FILES_MULTIPLE: 5,
  MAX_FIELDS: 10,
  MAX_FIELD_NAME_SIZE: 100,
  MAX_FIELD_SIZE: 1024,
  MAX_HEADER_PAIRS: 2000,

  IMAGE_MIME_TYPES: ['image/png', 'image/jpeg', 'image/jpg'] as const,

  DOCUMENT_MIME_TYPES: [
    'text/csv',
    'application/pdf',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  ] as const,

  ALL_ALLOWED_MIME_TYPES: [
    'image/png',
    'image/jpeg',
    'image/jpg',
    'text/csv',
    'application/pdf',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  ] as const,

  IMAGE_EXTENSIONS: /\.(png|jpeg|jpg)$/i,
  DOCUMENT_EXTENSIONS: /\.(csv|pdf|docx|pptx)$/i,
  ALL_EXTENSIONS: /\.(png|jpeg|jpg|csv|pdf|docx|pptx)$/i,

  ERRORS: {
    INVALID_IMAGE_TYPE:
      'Invalid file type. Only PNG, JPEG, and JPG images are allowed.',
    INVALID_DOCUMENT_TYPE:
      'Invalid file type. Only CSV, PDF, DOCX, and PPTX documents are allowed.',
    INVALID_FILE_TYPE:
      'Invalid file type. Only PNG, JPEG, JPG, CSV, PDF, DOCX, and PPTX files are allowed.',
    IMAGE_TOO_LARGE: 'Image is too large. Maximum image size is 2MB.',
    FILE_TOO_LARGE: 'File is too large. Maximum file size is 10MB.',
    TOO_MANY_FILES: 'Too many files uploaded.',
  },
} as const;

export type AllowedMimeType =
  (typeof UPLOAD_SECURITY_CONFIG.ALL_ALLOWED_MIME_TYPES)[number];
