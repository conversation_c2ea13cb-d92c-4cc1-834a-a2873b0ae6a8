/**
 * Upload security configuration constants
 * Centralized configuration for file upload limits and security settings
 */

export const UPLOAD_SECURITY_CONFIG = {
  MAX_FILE_SIZE: 10 * 1024 * 1024,
  MAX_FILES: 1,
  MAX_FIELDS: 10,
  MAX_FIELD_NAME_SIZE: 100,
  MAX_FIELD_SIZE: 1024,
  MAX_HEADER_PAIRS: 2000,

  ALLOWED_MIME_TYPES: [
    'image/png',
    'image/jpeg',
    'image/jpg',
    'text/csv',
    'application/pdf',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  ] as const,

  ALLOWED_IMAGE_EXTENSIONS: /\.(png|jpeg|jpg)$/i,
  ALLOWED_FILE_EXTENSIONS: /\.(csv|pdf|docx|pptx)$/i,

  ERRORS: {
    INVALID_FILE_TYPE:
      'Invalid file type. Only PNG, JPEG, JPG, CSV, PDF, DOCX, and PPTX files are allowed.',
    FILE_TOO_LARGE: 'File is too large. Maximum file size is 10MB.',
    TOO_MANY_FILES: 'Too many files. Only 1 file is allowed per upload.',
  },
} as const;

export type AllowedMimeType =
  (typeof UPLOAD_SECURITY_CONFIG.ALLOWED_MIME_TYPES)[number];
