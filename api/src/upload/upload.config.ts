/**
 * Upload security configuration constants
 * Centralized configuration for file upload limits and security settings
 */

export const UPLOAD_SECURITY_CONFIG = {
  // File size limits
  MAX_FILE_SIZE: 2 * 1024 * 1024, // 2MB
  
  // File count limits
  MAX_FILES: 1, // Single file upload for security
  
  // Form field limits
  MAX_FIELDS: 10, // Maximum number of form fields
  MAX_FIELD_NAME_SIZE: 100, // Maximum field name length
  MAX_FIELD_SIZE: 1024, // Maximum field value size
  
  // Request limits
  MAX_HEADER_PAIRS: 2000, // Prevent header pollution attacks
  
  // Allowed file types
  ALLOWED_MIME_TYPES: ['image/png', 'image/jpeg', 'image/jpg'] as const,
  ALLOWED_FILE_EXTENSIONS: /\.(png|jpeg|jpg)$/i,
  
  // Error messages
  ERRORS: {
    INVALID_FILE_TYPE: 'Invalid file type. Only PNG, JPEG, and JPG files are allowed.',
    FILE_TOO_LARGE: 'File is too large. Maximum file size is 2MB.',
    TOO_MANY_FILES: 'Too many files. Only 1 file is allowed per upload.',
  },
} as const;

export type AllowedMimeType = typeof UPLOAD_SECURITY_CONFIG.ALLOWED_MIME_TYPES[number];
