import { FileValidator } from '@nestjs/common';
import { ParseFilePipe, PipeTransform } from '@nestjs/common';
import { UPLOAD_SECURITY_CONFIG } from '@/upload/upload.config';

export class CustomMaxFileSizeValidator extends FileValidator<{
  maxSize: number;
}> {
  constructor(
    protected override readonly validationOptions: { maxSize: number },
  ) {
    super(validationOptions);
  }

  isValid(file: Express.Multer.File): boolean {
    return file.size <= this.validationOptions.maxSize;
  }

  buildErrorMessage(): string {
    const maxSizeMB = this.validationOptions.maxSize / (1024 * 1024);
    return `File is too large. Maximum file size is ${maxSizeMB}MB`;
  }
}

export class ParseFilesPipe
  extends ParseFilePipe
  implements PipeTransform<Express.Multer.File[]>
{
  override async transform(
    files: Express.Multer.File[] | { [key: string]: Express.Multer.File[] },
  ) {
    for (const file of Object.values(files).flat()) await super.transform(file);

    return files;
  }
}
